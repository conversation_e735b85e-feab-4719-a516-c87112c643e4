// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	appservice2 "brandreviews/application/brand/appservice"
	"brandreviews/application/coupon/appservice"
	"brandreviews/application/task/scheduler"
	service3 "brandreviews/application/task/service"
	"brandreviews/config"
	service2 "brandreviews/domain/brand/service"
	"brandreviews/domain/coupon/service"
	"brandreviews/infra/cache"
	"brandreviews/infra/dao"
	"brandreviews/infra/database"
	"github.com/google/wire"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// Injectors from wire.go:

// InitializeTaskApplication 初始化任务应用程序
func InitializeTaskApplication(cfg *config.Config) (*TaskApplication, error) {
	logger, err := ProvideLogger(cfg)
	if err != nil {
		return nil, err
	}
	db, err := ProvidePostgres(cfg)
	if err != nil {
		return nil, err
	}
	couponRepository := dao.NewCouponPostgresRepository(db)
	couponService := service.NewCouponService(couponRepository, logger)
	couponAppService := appservice.NewCouponAppService(couponService)
	cacheManager := ProvideCacheManager()
	brandRepository := dao.NewBrandPostgresRepository(db, cacheManager, logger)
	brandService := service2.NewBrandService(brandRepository, logger)
	brandAppService := appservice2.NewBrandAppService(brandService)
	syncService := service3.NewSyncService(cfg, logger, couponAppService, brandAppService)
	schedulerScheduler, err := scheduler.NewScheduler(cfg, logger, syncService)
	if err != nil {
		return nil, err
	}
	taskApplication := &TaskApplication{
		Scheduler:  schedulerScheduler,
		Logger:     logger,
		Config:     cfg,
		PostgresDB: db,
	}
	return taskApplication, nil
}

// wire.go:

// ProviderSet 定义所有的依赖提供者
var ProviderSet = wire.NewSet(

	ProvidePostgres,
	ProvideLogger,
	ProvideCacheManager, dao.NewCouponPostgresRepository, dao.NewBrandPostgresRepository, service.NewCouponService, service2.NewBrandService, appservice.NewCouponAppService, appservice2.NewBrandAppService, service3.NewSyncService, scheduler.NewScheduler,
)

type TaskApplication struct {
	Scheduler  scheduler.Scheduler
	Logger     *zap.Logger
	Config     *config.Config
	PostgresDB *gorm.DB
}

// ProvidePostgres 提供 PostgreSQL 连接
func ProvidePostgres(cfg *config.Config) (*gorm.DB, error) {
	return database.NewPostgresDB(cfg.Postgres)
}

// ProvideLogger 提供日志记录器
func ProvideLogger(cfg *config.Config) (*zap.Logger, error) {
	var logger *zap.Logger
	var err error

	if cfg.Logger.Level == "debug" {
		logger, err = zap.NewDevelopment()
	} else {
		logger, err = zap.NewProduction()
	}

	if err != nil {
		return nil, err
	}

	return logger, nil
}

// ProvideCacheManager 提供缓存管理器
func ProvideCacheManager() *cache.CacheManager {
	return cache.NewCacheManager()
}

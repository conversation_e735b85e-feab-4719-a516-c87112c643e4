package linkbuxvo

type GetCouponListResp struct {
	Status struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	} `json:"status"`
	Data struct {
		TotalItems int `json:"total_items"`
		TotalPage  int `json:"total_page"`
		Limit      int `json:"limit"`
		Page       int `json:"page"`
		Data       []struct {
			CouponId      string      `json:"coupon_id"`
			Mid           string      `json:"mid"`
			Mcid          string      `json:"mcid"`
			MerchantName  string      `json:"merchant_name"`
			PrimaryRegion interface{} `json:"primary_region"`
			OfferType     interface{} `json:"offer_type"`
			CouponName    string      `json:"coupon_name"`
			CouponCode    string      `json:"coupon_code"`
			Discount      string      `json:"discount"`
			Description   string      `json:"description"`
			BeginDate     string      `json:"begin_date"`
			EndDate       string      `json:"end_date"`
			OriginUrl     string      `json:"origin_url"`
			TrackingUrl   string      `json:"tracking_url"`
			Logo          string      `json:"logo"`
			Category      string      `json:"category"`
		} `json:"data"`
	} `json:"data"`
}

type GetMerchantsResp struct {
	Status struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	} `json:"status"`
	Data struct {
		TotalMcid string `json:"total_mcid"`
		TotalPage int    `json:"total_page"`
		Limit     int    `json:"limit"`
		List      []struct {
			Mcid               string      `json:"mcid"`
			Mid                string      `json:"mid"`
			MerchantName       string      `json:"merchant_name"`
			CommRate           interface{} `json:"comm_rate"`
			CommDetail         interface{} `json:"comm_detail"`
			CommDetailUsd      interface{} `json:"comm_detail_usd"`
			SiteUrl            string      `json:"site_url"`
			Logo               string      `json:"logo"`
			Categories         string      `json:"categories"`
			Tags               string      `json:"tags"`
			OfferType          string      `json:"offer_type"`
			NetworkPartner     string      `json:"network_partner"`
			AvgPaymentCycle    string      `json:"avg_payment_cycle"`
			AvgPayout          string      `json:"avg_payout"`
			SupportDeeplink    string      `json:"support_deeplink"`
			PrimaryRegion      string      `json:"primary_region"`
			SupportRegion      string      `json:"support_region"`
			MerchantStatus     string      `json:"merchant_status"`
			Datetime           string      `json:"datetime"`
			Relationship       string      `json:"relationship"`
			TrackingUrl        string      `json:"tracking_url"`
			TrackingUrlShort   string      `json:"tracking_url_short"`
			TrackingUrlSmart   string      `json:"tracking_url_smart"`
			PromotionalMethods struct {
				InternalUseOnlyPartnerCollaboration string `json:"(Internal Use Only) Partner Collaboration"`
				AppPlugIn                           string `json:"App/Plug-In"`
				Cashback                            string `json:"Cashback"`
				ComparisonShoppingServiceCSS        string `json:"Comparison Shopping Service (CSS)"`
				Content                             string `json:"Content"`
				ContentPREMIUM                      string `json:"Content - PREMIUM"`
				CouponDeal                          string `json:"Coupon/Deal"`
				EMail                               string `json:"E-mail"`
				Influencer                          string `json:"Influencer"`
				InfluencerPREMIUM                   string `json:"Influencer - PREMIUM"`
				Loyalty                             string `json:"Loyalty"`
				MediaBuyer                          string `json:"Media Buyer"`
				PriceComparison                     string `json:"Price Comparison"`
				SocialMediaGroup                    string `json:"Social Media Group"`
				Subnetwork                          string `json:"Subnetwork"`
				TMBidding                           string `json:"TM+ Bidding"`
				WebPortal                           string `json:"Web Portal"`
			} `json:"promotional_methods"`
			Rd string `json:"rd"`
		} `json:"list"`
	} `json:"data"`
}

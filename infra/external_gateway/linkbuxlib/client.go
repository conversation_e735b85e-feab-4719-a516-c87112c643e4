package linkbuxlib

import (
	"brandreviews/infra/ecode"
	"brandreviews/infra/external_gateway/linkbuxlib/linkbuxvo"
	"brandreviews/infra/utils/domainutil"
	"context"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"net/http"
	"strconv"
	"strings"
)

func GetCoupons(token string, limit int, page int) (*linkbuxvo.GetCouponListResp, *ecode.Error) {
	ctx := context.Background()

	params := map[string]interface{}{
		"token": token,
		"mod":   "coupon",
		"op":    "coupons",
		"limit": strconv.Itoa(limit),
		"page":  strconv.Itoa(page),
	}

	headers := map[string]string{
		"Content-Type": "application/x-www-form-urlencoded",
	}

	// Add small delay to prevent rate limiting
	resp, err := remoteInvokeWithUrl(ctx, host+apiGetCoupons, http.MethodGet, params, headers, nil)
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}

	couponList := new(linkbuxvo.GetCouponListResp)
	if err := json.Unmarshal(resp, couponList); err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}

	return couponList, nil
}

func BatchGetCoupons(token string, limit int) ([]map[string]interface{}, *ecode.Error) {
	var createDataRows []map[string]interface{}
	uniqueMap := make(map[string]bool)
	page := 1

	for {
		resp, err := GetCoupons(token, limit, page)
		if err != nil {
			fmt.Println("GetCoupons:", err)
			continue
		}

		for _, row := range resp.Data.Data {
			if len(row.CouponCode) <= 0 {
				continue
			}

			uniqueKey := row.CouponId
			if _, exists := uniqueMap[uniqueKey]; !exists {
				rowData := map[string]interface{}{}
				rowData["coupon_title"] = row.CouponName
				rowData["coupon_code"] = row.CouponCode
				rowData["discount"] = row.Discount
				rowData["platform_type"] = "linkbux"
				rowData["description"] = row.Description
				rowData["started_at"] = row.BeginDate
				rowData["ended_at"] = row.EndDate
				rowData["original_domain"] = row.OriginUrl
				rowData["domain"] = domainutil.ExtractDomain(row.OriginUrl)
				createDataRows = append(createDataRows, rowData)
				uniqueMap[uniqueKey] = true
			}
		}
		fmt.Println(page, resp.Data.TotalPage)
		// Check if we've reached the last page
		if page >= resp.Data.TotalPage {
			break
		}
		page++
	}
	return createDataRows, nil
}

func GetMerchants(token string, limit int, page int, relationship string) (*linkbuxvo.GetMerchantsResp, *ecode.Error) {
	ctx := context.Background()

	params := map[string]interface{}{
		"mod":          "medium",
		"op":           "monetization_api",
		"token":        token,
		"relationship": relationship,
		"limit":        strconv.Itoa(limit),
		"page":         strconv.Itoa(page),
	}

	resp, err := remoteInvokeWithUrl(ctx, host+apiGetMerchants, http.MethodGet, params, nil, nil)
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}

	getMerchantsResp := new(linkbuxvo.GetMerchantsResp)
	err = json.Unmarshal(resp, getMerchantsResp)
	if err != nil {
		zap.L().Error("partnerboostlib GetMerchants json.Unmarshal failed", zap.Error(err))
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}
	return getMerchantsResp, nil
}

func BatchGetMerchants(token string, limit int, relationship string) ([]map[string]interface{}, *ecode.Error) {
	allMerchants := make([]map[string]interface{}, 0)
	uniqueMap := make(map[string]bool)
	page := 1

	for {
		// 调用 GetMerchants 函数
		getMerchantsResp, err := GetMerchants(token, limit, page, relationship)
		if err != nil {
			zap.L().Error("linkbuxlib BatchGetMerchants GetMerchants failed", zap.Error(err))
			return allMerchants, err
		}

		// 将获取到的商家添加到总列表中
		for _, merchant := range getMerchantsResp.Data.List {
			uniqueKey := merchant.Mid
			if _, exists := uniqueMap[uniqueKey]; !exists {
				merchantRow := map[string]interface{}{}
				merchantRow["slug"] = strings.ReplaceAll(strings.ToLower(merchant.Mcid+"b6a3"), " ", "")
				merchantRow["name"] = merchant.MerchantName
				merchantRow["description"] = ""
				merchantRow["logo"] = "https://logo.clearbit.com/" + domainutil.ExtractDomain(merchant.SiteUrl)
				merchantRow["website"] = domainutil.ExtractDomain(merchant.SiteUrl)
				merchantRow["affiliate_link"] = merchant.TrackingUrl
				merchantRow["category_slug"] = merchant.Categories
				merchantRow["featured"] = true
				merchantRow["active"] = true
				allMerchants = append(allMerchants, merchantRow)
				uniqueMap[uniqueKey] = true
			}
		}

		// 如果当前页是最后一页，退出循环
		if page >= getMerchantsResp.Data.TotalPage {
			break
		}

		// 更新页码，获取下一页
		page = page + 1
	}

	return allMerchants, nil
}

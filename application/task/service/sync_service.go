package service

import (
	"net/url"
	"strings"
	"time"

	brandapp "brandreviews/application/brand/appservice"
	branddto "brandreviews/application/brand/dto"
	couponapp "brandreviews/application/coupon/appservice"
	coupondto "brandreviews/application/coupon/dto"
	"brandreviews/config"
	brandentity "brandreviews/domain/brand/entity"
	couponentity "brandreviews/domain/coupon/entity"
	"brandreviews/infra/ecode"
	"brandreviews/infra/external_gateway/linkbuxlib"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type SyncService interface {
	SyncCouponList(ctx *gin.Context) *ecode.Error
}

type SyncServiceImpl struct {
	config        *config.Config
	logger        *zap.Logger
	couponService couponapp.CouponAppService
	brandService  brandapp.BrandAppService
}

func NewSyncService(
	config *config.Config,
	logger *zap.Logger,
	couponService couponapp.CouponAppService,
	brandService brandapp.BrandAppService,
) SyncService {
	return &SyncServiceImpl{
		config:        config,
		logger:        logger,
		couponService: couponService,
		brandService:  brandService,
	}
}

func (s *SyncServiceImpl) SyncCouponList(ctx *gin.Context) *ecode.Error {
	s.logger.Info("开始同步 coupon 信息")

	// 获取现有的优惠券
	existingCoupons, err := s.getAllExistingCoupons(ctx)
	if err != nil {
		s.logger.Error("获取现有优惠券失败", zap.Error(err))
		return err
	}

	// 获取现有的品牌信息
	existingBrands, err := s.getAllExistingBrands(ctx)
	if err != nil {
		s.logger.Error("获取现有品牌失败", zap.Error(err))
		return err
	}

	// 创建品牌域名映射，用于快速查找
	brandDomainMap := make(map[string]uint64)
	for _, brand := range existingBrands {
		if brand.Website != "" {
			domain := s.extractDomain(brand.Website)
			if domain != "" {
				brandDomainMap[domain] = brand.Id
			}
		}
	}

	// 创建coupon code映射，用于快速查找
	couponCodeMap := make(map[string]*couponentity.Coupon)
	for _, c := range existingCoupons {
		couponCodeMap[c.Code] = c
	}

	// 遍历配置中的优惠券账户
	for _, accountConfig := range s.config.Task.DataCollection.CouponsAccounts {
		platformType := accountConfig.Type
		var coupons []*couponentity.Coupon

		if platformType == "linkbux" {
			token := accountConfig.Token
			limit := accountConfig.Limit
			// 获取优惠券数据
			couponsData, err := s.fetchCouponsFromLinkBux(token, limit)
			if err != nil {
				s.logger.Error("linkbux获取优惠券数据失败", zap.Error(err))
				continue
			}
			coupons = s.toEntityCreateCoupon(couponsData, brandDomainMap)
		}

		// 创建或更新优惠券信息
		var createCoupons []*couponentity.Coupon
		uniqueCouponMap := make(map[string]bool)
		for _, coupon := range coupons {
			if _, exists := uniqueCouponMap[coupon.Code]; exists {
				continue
			}
			if _, exists := couponCodeMap[coupon.Code]; !exists {
				createCoupons = append(createCoupons, coupon)
			}
			uniqueCouponMap[coupon.Code] = true
		}

		// 批量创建新优惠券
		if len(createCoupons) > 0 {
			if err := s.batchCreateCoupons(ctx, createCoupons); err != nil {
				s.logger.Error("批量创建coupon失败",
					zap.String("platform_type", platformType),
					zap.Error(err))
			} else {
				s.logger.Info("成功创建优惠券",
					zap.String("platform_type", platformType),
					zap.Int("count", len(createCoupons)))
			}
		}
	}

	s.logger.Info("同步coupon信息完成")
	return nil
}

// getAllExistingCoupons 获取所有现有优惠券
func (s *SyncServiceImpl) getAllExistingCoupons(ctx *gin.Context) ([]*couponentity.Coupon, *ecode.Error) {
	// 使用分页方式获取所有优惠券，避免一次性加载过多数据
	var allCoupons []*couponentity.Coupon
	page := 1
	pageSize := 1000 // 每页1000条记录

	for {
		// 创建请求参数
		req := &coupondto.GetCouponListReq{
			Page:     page,
			PageSize: pageSize,
		}

		// 获取优惠券列表
		resp, err := s.couponService.GetCouponListByCondition(ctx, req)
		if err != nil {
			s.logger.Error("获取优惠券列表失败", zap.Int("page", page), zap.Error(err))
			return nil, err
		}

		// 如果没有数据，退出循环
		if len(resp.CouponList) == 0 {
			break
		}

		// 将DTO转换为实体
		for _, couponDto := range resp.CouponList {
			coupon := &couponentity.Coupon{
				Id:             couponDto.Id,
				Slug:           couponDto.Slug,
				Title:          couponDto.Title,
				Description:    couponDto.Description,
				Code:           couponDto.Code,
				CouponUrl:      couponDto.CouponUrl,
				DiscountType:   couponDto.DiscountType,
				DiscountValue:  couponDto.DiscountValue,
				MinOrderValue:  couponDto.MinOrderValue,
				MaxDiscount:    couponDto.MaxDiscount,
				Featured:       couponDto.Featured,
				Active:         couponDto.Active,
				Verified:       couponDto.Verified,
				StartDate:      couponDto.StartDate,
				EndDate:        couponDto.EndDate,
				UsageLimit:     couponDto.UsageLimit,
				UsedCount:      couponDto.UsedCount,
				UserUsageLimit: couponDto.UserUsageLimit,
				BrandId:        couponDto.BrandId,
				BrandSlug:      couponDto.BrandSlug,
				CategoryId:     couponDto.CategoryId,
				CategorySlug:   couponDto.CategorySlug,
			}
			allCoupons = append(allCoupons, coupon)
		}

		// 如果返回的数据少于页面大小，说明已经是最后一页
		if len(resp.CouponList) < pageSize {
			break
		}

		page++
	}

	s.logger.Info("成功获取所有现有优惠券", zap.Int("total", len(allCoupons)))
	return allCoupons, nil
}

// getAllExistingBrands 获取所有现有品牌
func (s *SyncServiceImpl) getAllExistingBrands(ctx *gin.Context) ([]*brandentity.Brand, *ecode.Error) {
	// 使用分页方式获取所有品牌，避免一次性加载过多数据
	var allBrands []*brandentity.Brand
	page := 1
	pageSize := 1000 // 每页1000条记录

	for {
		// 创建请求参数
		req := &branddto.GetBrandListReq{
			Page:     page,
			PageSize: pageSize,
		}

		// 获取品牌列表
		resp, err := s.brandService.GetBrandListByCondition(ctx, req)
		if err != nil {
			s.logger.Error("获取品牌列表失败", zap.Int("page", page), zap.Error(err))
			return nil, err
		}

		// 如果没有数据，退出循环
		if len(resp.BrandList) == 0 {
			break
		}

		// 将DTO转换为实体
		for _, brandDto := range resp.BrandList {
			brand := &brandentity.Brand{
				Id:           brandDto.Id,
				Slug:         brandDto.Slug,
				Name:         brandDto.Name,
				Description:  brandDto.Description,
				Logo:         brandDto.Logo,
				Website:      brandDto.Website,
				Featured:     brandDto.Featured,
				CategorySlug: brandDto.CategorySlug,
			}
			allBrands = append(allBrands, brand)
		}

		// 如果返回的数据少于页面大小，说明已经是最后一页
		if len(resp.BrandList) < pageSize {
			break
		}

		page++
	}

	s.logger.Info("成功获取所有现有品牌", zap.Int("total", len(allBrands)))
	return allBrands, nil
}

// extractDomain 从URL中提取域名
func (s *SyncServiceImpl) extractDomain(website string) string {
	if website == "" {
		return ""
	}

	// 如果没有协议前缀，添加http://
	if !strings.HasPrefix(website, "http://") && !strings.HasPrefix(website, "https://") {
		website = "http://" + website
	}

	parsedURL, err := url.Parse(website)
	if err != nil {
		s.logger.Warn("解析URL失败", zap.String("website", website), zap.Error(err))
		return ""
	}

	domain := strings.ToLower(parsedURL.Hostname())
	// 移除www前缀
	if strings.HasPrefix(domain, "www.") {
		domain = domain[4:]
	}

	return domain
}

// fetchCouponsFromLinkBux 从LinkBux获取优惠券数据
func (s *SyncServiceImpl) fetchCouponsFromLinkBux(token string, limit int) ([]map[string]interface{}, error) {
	s.logger.Info("开始从LinkBux获取优惠券数据", zap.String("token", token), zap.Int("limit", limit))

	// 使用LinkBux客户端批量获取优惠券数据
	couponsData, err := linkbuxlib.BatchGetCoupons(token, limit)
	if err != nil {
		s.logger.Error("LinkBux API调用失败", zap.Error(err))
		return nil, err
	}

	s.logger.Info("成功从LinkBux获取优惠券数据", zap.Int("count", len(couponsData)))
	return couponsData, nil
}

// batchCreateCoupons 批量创建优惠券
func (s *SyncServiceImpl) batchCreateCoupons(ctx *gin.Context, coupons []*couponentity.Coupon) error {
	// 由于当前的CouponAppService没有BatchCreate方法，我们需要逐个创建
	successCount := 0
	errorCount := 0

	for _, coupon := range coupons {
		// 通过domain service创建优惠券
		// 注意：这里我们需要通过应用服务层来创建，但当前的应用服务层没有Create方法
		// 我们需要直接使用domain service，但这需要修改架构
		// 为了保持架构一致性，我们暂时记录日志并计划后续添加Create方法到应用服务层

		s.logger.Info("准备创建优惠券",
			zap.String("code", coupon.Code),
			zap.String("title", coupon.Title),
			zap.Uint64("brand_id", coupon.BrandId))

		// TODO: 需要在CouponAppService中添加CreateCoupon方法
		// 目前暂时只记录日志，实际部署时需要实现真正的创建逻辑
		// err := s.couponService.CreateCoupon(ctx, coupon)
		// if err != nil {
		//     s.logger.Error("创建优惠券失败",
		//         zap.String("code", coupon.Code),
		//         zap.Error(err))
		//     errorCount++
		//     continue
		// }

		successCount++
	}

	s.logger.Info("批量创建优惠券完成",
		zap.Int("success_count", successCount),
		zap.Int("error_count", errorCount),
		zap.Int("total", len(coupons)))

	return nil
}

// toEntityCreateCoupon 将外部数据转换为优惠券实体
func (s *SyncServiceImpl) toEntityCreateCoupon(createDataRows []map[string]interface{}, brandDomainMap map[string]uint64) []*couponentity.Coupon {
	couponList := []*couponentity.Coupon{}

	for _, data := range createDataRows {
		coupon := &couponentity.Coupon{}

		// 根据域名关联品牌
		if domainInterface, ok := data["domain"]; ok {
			if domain, ok := domainInterface.(string); ok && domain != "" {
				domain = s.extractDomain(domain)
				if brandId, exists := brandDomainMap[domain]; exists {
					coupon.BrandId = brandId
				} else {
					// 如果找不到对应的品牌，跳过这个优惠券
					s.logger.Warn("找不到对应的品牌", zap.String("domain", domain))
					continue
				}
			} else {
				// 如果没有域名信息，跳过这个优惠券
				continue
			}
		} else {
			// 如果没有域名信息，跳过这个优惠券
			continue
		}

		// 验证优惠券代码
		if codeInterface, ok := data["coupon_code"]; ok {
			if code, ok := codeInterface.(string); ok && len(code) > 0 && code != "No Coupons Needed" {
				coupon.Code = code
				// 生成slug，使用code的小写版本
				coupon.Slug = strings.ToLower(strings.ReplaceAll(code, " ", "-"))
			} else {
				continue
			}
		} else {
			continue
		}

		// 设置优惠券信息
		if titleInterface, ok := data["coupon_title"]; ok {
			if title, ok := titleInterface.(string); ok {
				coupon.Title = title
			}
		}

		if descInterface, ok := data["description"]; ok {
			if desc, ok := descInterface.(string); ok {
				coupon.Description = desc
			}
		}

		// 设置折扣类型，默认为percentage
		coupon.DiscountType = "percentage"

		if discountInterface, ok := data["discount"]; ok {
			if discountStr, ok := discountInterface.(string); ok {
				// 这里可以解析折扣值，但需要根据实际的数据格式来处理
				// 暂时设置为0，实际实现时需要解析字符串中的数值
				_ = discountStr // 避免未使用变量警告
				coupon.DiscountValue = 0
			}
		}

		// 设置优惠券URL，优先使用tracking_url，如果没有则使用original_domain
		if trackingUrlInterface, ok := data["tracking_url"]; ok {
			if trackingUrl, ok := trackingUrlInterface.(string); ok && trackingUrl != "" {
				coupon.CouponUrl = trackingUrl
			}
		} else if originalDomainInterface, ok := data["original_domain"]; ok {
			if originalDomain, ok := originalDomainInterface.(string); ok && originalDomain != "" {
				coupon.CouponUrl = originalDomain
			}
		} else {
			// 如果都没有URL，跳过这个优惠券
			s.logger.Warn("优惠券缺少URL信息", zap.String("code", coupon.Code))
			continue
		}

		// 解析日期
		if startedAtInterface, ok := data["started_at"]; ok {
			if startedAtStr, ok := startedAtInterface.(string); ok {
				if startedAtDate, err := time.Parse("2006-01-02", startedAtStr); err == nil {
					coupon.StartDate = uint64(startedAtDate.Unix())
				} else {
					s.logger.Warn("日期解析错误", zap.Error(err), zap.String("started_at", startedAtStr))
					continue
				}
			}
		}

		if endedAtInterface, ok := data["ended_at"]; ok {
			if endedAtStr, ok := endedAtInterface.(string); ok {
				if endedAtDate, err := time.Parse("2006-01-02", endedAtStr); err == nil {
					coupon.EndDate = uint64(endedAtDate.Unix())
				} else {
					s.logger.Warn("日期解析错误", zap.Error(err), zap.String("ended_at", endedAtStr))
					continue
				}
			}
		}

		// 设置默认值
		coupon.Active = true
		coupon.Featured = false
		coupon.Verified = false
		coupon.UsageLimit = 0 // 无限制
		coupon.UsedCount = 0
		coupon.UserUsageLimit = 1

		coupon.CreatedAt = time.Now()
		coupon.UpdatedAt = time.Now()
		couponList = append(couponList, coupon)
	}

	return couponList
}

// BrandInfo 临时定义，用于品牌信息
type BrandInfo struct {
	Id      uint64 `json:"id"`
	Website string `json:"website"`
}

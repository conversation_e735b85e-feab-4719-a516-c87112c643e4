package service

import (
	"context"
	"net/url"
	"strings"
	"time"

	brandapp "brandreviews/application/brand/appservice"
	branddto "brandreviews/application/brand/dto"
	couponapp "brandreviews/application/coupon/appservice"
	coupondto "brandreviews/application/coupon/dto"
	"brandreviews/config"
	brandentity "brandreviews/domain/brand/entity"
	couponentity "brandreviews/domain/coupon/entity"
	"brandreviews/infra/ecode"
	"brandreviews/infra/external_gateway/linkbuxlib"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type SyncService interface {
	SyncCouponList(ctx *gin.Context) *ecode.Error
	SyncCouponListWithContext(ctx context.Context) error
}

type SyncServiceImpl struct {
	config        *config.Config
	logger        *zap.Logger
	couponService couponapp.CouponAppService
	brandService  brandapp.BrandAppService
}

func NewSyncService(
	config *config.Config,
	logger *zap.Logger,
	couponService couponapp.CouponAppService,
	brandService brandapp.BrandAppService,
) SyncService {
	return &SyncServiceImpl{
		config:        config,
		logger:        logger,
		couponService: couponService,
		brandService:  brandService,
	}
}

func (s *SyncServiceImpl) SyncCouponList(ctx *gin.Context) *ecode.Error {
	s.logger.Info("开始同步 coupon 信息")

	// 获取现有的优惠券
	existingCoupons, err := s.getAllExistingCoupons(ctx)
	if err != nil {
		s.logger.Error("获取现有优惠券失败", zap.Error(err))
		return err
	}

	// 获取现有的品牌信息
	existingBrands, err := s.getAllExistingBrands(ctx)
	if err != nil {
		s.logger.Error("获取现有品牌失败", zap.Error(err))
		return err
	}

	// 创建品牌域名映射，用于快速查找
	brandDomainMap := make(map[string]uint64)
	for _, brand := range existingBrands {
		if brand.Website != "" {
			domain := s.extractDomain(brand.Website)
			if domain != "" {
				brandDomainMap[domain] = brand.Id
			}
		}
	}

	// 创建coupon code映射，用于快速查找
	couponCodeMap := make(map[string]*couponentity.Coupon)
	for _, c := range existingCoupons {
		couponCodeMap[c.Code] = c
	}

	// 遍历配置中的优惠券账户
	for _, accountConfig := range s.config.Task.DataCollection.CouponsAccounts {
		platformType := accountConfig.Type
		var coupons []*couponentity.Coupon

		if platformType == "linkbux" {
			token := accountConfig.Token
			limit := accountConfig.Limit
			// 获取优惠券数据
			couponsData, err := s.fetchCouponsFromLinkBux(token, limit)
			if err != nil {
				s.logger.Error("linkbux获取优惠券数据失败", zap.Error(err))
				continue
			}
			coupons = s.toEntityCreateCoupon(couponsData, brandDomainMap)
		}

		// 创建或更新优惠券信息
		var createCoupons []*couponentity.Coupon
		uniqueCouponMap := make(map[string]bool)
		for _, coupon := range coupons {
			if _, exists := uniqueCouponMap[coupon.Code]; exists {
				continue
			}
			if _, exists := couponCodeMap[coupon.Code]; !exists {
				createCoupons = append(createCoupons, coupon)
			}
			uniqueCouponMap[coupon.Code] = true
		}

		// 批量创建新优惠券
		if len(createCoupons) > 0 {
			if err := s.batchCreateCoupons(ctx, createCoupons); err != nil {
				s.logger.Error("批量创建coupon失败",
					zap.String("platform_type", platformType),
					zap.Error(err))
			} else {
				s.logger.Info("成功创建优惠券",
					zap.String("platform_type", platformType),
					zap.Int("count", len(createCoupons)))
			}
		}
	}

	s.logger.Info("同步coupon信息完成")
	return nil
}

// getAllExistingCoupons 获取所有现有优惠券
func (s *SyncServiceImpl) getAllExistingCoupons(ctx *gin.Context) ([]*couponentity.Coupon, *ecode.Error) {
	// 使用分页方式获取所有优惠券，避免一次性加载过多数据
	var allCoupons []*couponentity.Coupon
	page := 1
	pageSize := 1000 // 每页1000条记录

	for {
		// 创建请求参数
		req := &coupondto.GetCouponListReq{
			Page:     page,
			PageSize: pageSize,
		}

		// 获取优惠券列表
		resp, err := s.couponService.GetCouponListByCondition(ctx, req)
		if err != nil {
			s.logger.Error("获取优惠券列表失败", zap.Int("page", page), zap.Error(err))
			return nil, err
		}

		// 如果没有数据，退出循环
		if len(resp.CouponList) == 0 {
			break
		}

		// 将DTO转换为实体
		for _, couponDto := range resp.CouponList {
			coupon := &couponentity.Coupon{
				Id:             couponDto.Id,
				Slug:           couponDto.Slug,
				Title:          couponDto.Title,
				Description:    couponDto.Description,
				Code:           couponDto.Code,
				CouponUrl:      couponDto.CouponUrl,
				DiscountType:   couponDto.DiscountType,
				DiscountValue:  couponDto.DiscountValue,
				MinOrderValue:  couponDto.MinOrderValue,
				MaxDiscount:    couponDto.MaxDiscount,
				Featured:       couponDto.Featured,
				Active:         couponDto.Active,
				Verified:       couponDto.Verified,
				StartDate:      couponDto.StartDate,
				EndDate:        couponDto.EndDate,
				UsageLimit:     couponDto.UsageLimit,
				UsedCount:      couponDto.UsedCount,
				UserUsageLimit: couponDto.UserUsageLimit,
				BrandId:        couponDto.BrandId,
				BrandSlug:      couponDto.BrandSlug,
				CategoryId:     couponDto.CategoryId,
				CategorySlug:   couponDto.CategorySlug,
			}
			allCoupons = append(allCoupons, coupon)
		}

		// 如果返回的数据少于页面大小，说明已经是最后一页
		if len(resp.CouponList) < pageSize {
			break
		}

		page++
	}

	s.logger.Info("成功获取所有现有优惠券", zap.Int("total", len(allCoupons)))
	return allCoupons, nil
}

// getAllExistingBrands 获取所有现有品牌
func (s *SyncServiceImpl) getAllExistingBrands(ctx *gin.Context) ([]*brandentity.Brand, *ecode.Error) {
	// 使用分页方式获取所有品牌，避免一次性加载过多数据
	var allBrands []*brandentity.Brand
	page := 1
	pageSize := 1000 // 每页1000条记录

	for {
		// 创建请求参数
		req := &branddto.GetBrandListReq{
			Page:     page,
			PageSize: pageSize,
		}

		// 获取品牌列表
		resp, err := s.brandService.GetBrandListByCondition(ctx, req)
		if err != nil {
			s.logger.Error("获取品牌列表失败", zap.Int("page", page), zap.Error(err))
			return nil, err
		}

		// 如果没有数据，退出循环
		if len(resp.BrandList) == 0 {
			break
		}

		// 将DTO转换为实体
		for _, brandDto := range resp.BrandList {
			brand := &brandentity.Brand{
				Id:           brandDto.Id,
				Slug:         brandDto.Slug,
				Name:         brandDto.Name,
				Description:  brandDto.Description,
				Logo:         brandDto.Logo,
				Website:      brandDto.Website,
				Featured:     brandDto.Featured,
				CategorySlug: brandDto.CategorySlug,
			}
			allBrands = append(allBrands, brand)
		}

		// 如果返回的数据少于页面大小，说明已经是最后一页
		if len(resp.BrandList) < pageSize {
			break
		}

		page++
	}

	s.logger.Info("成功获取所有现有品牌", zap.Int("total", len(allBrands)))
	return allBrands, nil
}

// extractDomain 从URL中提取域名
func (s *SyncServiceImpl) extractDomain(website string) string {
	if website == "" {
		return ""
	}

	// 如果没有协议前缀，添加http://
	if !strings.HasPrefix(website, "http://") && !strings.HasPrefix(website, "https://") {
		website = "http://" + website
	}

	parsedURL, err := url.Parse(website)
	if err != nil {
		s.logger.Warn("解析URL失败", zap.String("website", website), zap.Error(err))
		return ""
	}

	domain := strings.ToLower(parsedURL.Hostname())
	// 移除www前缀
	if strings.HasPrefix(domain, "www.") {
		domain = domain[4:]
	}

	return domain
}

// fetchCouponsFromLinkBux 从LinkBux获取优惠券数据
func (s *SyncServiceImpl) fetchCouponsFromLinkBux(token string, limit int) ([]map[string]interface{}, error) {
	s.logger.Info("开始从LinkBux获取优惠券数据", zap.String("token", token), zap.Int("limit", limit))

	// 使用LinkBux客户端批量获取优惠券数据
	couponsData, err := linkbuxlib.BatchGetCoupons(token, limit)
	if err != nil {
		s.logger.Error("LinkBux API调用失败", zap.Error(err))
		return nil, err
	}

	s.logger.Info("成功从LinkBux获取优惠券数据", zap.Int("count", len(couponsData)))
	return couponsData, nil
}

// batchCreateCoupons 批量创建优惠券
func (s *SyncServiceImpl) batchCreateCoupons(ctx *gin.Context, coupons []*couponentity.Coupon) error {
	successCount := 0
	errorCount := 0

	for _, coupon := range coupons {
		s.logger.Info("创建优惠券",
			zap.String("code", coupon.Code),
			zap.String("title", coupon.Title),
			zap.Uint64("brand_id", coupon.BrandId))

		// 通过应用服务层创建优惠券
		err := s.couponService.CreateCoupon(ctx, coupon)
		if err != nil {
			s.logger.Error("创建优惠券失败",
				zap.String("code", coupon.Code),
				zap.String("title", coupon.Title),
				zap.Error(err))
			errorCount++
			continue
		}

		s.logger.Debug("优惠券创建成功",
			zap.String("code", coupon.Code),
			zap.Uint64("id", coupon.Id))
		successCount++
	}

	s.logger.Info("批量创建优惠券完成",
		zap.Int("success_count", successCount),
		zap.Int("error_count", errorCount),
		zap.Int("total", len(coupons)))

	if errorCount > 0 {
		return ecode.New(ecode.ErrInternalServer.Code, "部分优惠券创建失败")
	}

	return nil
}

// toEntityCreateCoupon 将外部数据转换为优惠券实体
func (s *SyncServiceImpl) toEntityCreateCoupon(createDataRows []map[string]interface{}, brandDomainMap map[string]uint64) []*couponentity.Coupon {
	couponList := []*couponentity.Coupon{}

	for _, data := range createDataRows {
		coupon := &couponentity.Coupon{}

		// 根据域名关联品牌
		if domainInterface, ok := data["domain"]; ok {
			if domain, ok := domainInterface.(string); ok && domain != "" {
				domain = s.extractDomain(domain)
				if brandId, exists := brandDomainMap[domain]; exists {
					coupon.BrandId = brandId
				} else {
					// 如果找不到对应的品牌，跳过这个优惠券
					s.logger.Warn("找不到对应的品牌", zap.String("domain", domain))
					continue
				}
			} else {
				// 如果没有域名信息，跳过这个优惠券
				continue
			}
		} else {
			// 如果没有域名信息，跳过这个优惠券
			continue
		}

		// 验证优惠券代码
		if codeInterface, ok := data["coupon_code"]; ok {
			if code, ok := codeInterface.(string); ok && len(code) > 0 && code != "No Coupons Needed" {
				coupon.Code = code
				// 生成slug，使用code的小写版本
				coupon.Slug = strings.ToLower(strings.ReplaceAll(code, " ", "-"))
			} else {
				continue
			}
		} else {
			continue
		}

		// 设置优惠券信息
		if titleInterface, ok := data["coupon_title"]; ok {
			if title, ok := titleInterface.(string); ok {
				coupon.Title = title
			}
		}

		if descInterface, ok := data["description"]; ok {
			if desc, ok := descInterface.(string); ok {
				coupon.Description = desc
			}
		}

		// 解析折扣信息
		if discountInterface, ok := data["discount"]; ok {
			if discountStr, ok := discountInterface.(string); ok && discountStr != "" {
				// 解析折扣字符串，提取数值和类型
				discountValue, discountType := s.parseDiscountString(discountStr)
				coupon.DiscountValue = discountValue
				coupon.DiscountType = discountType
			} else {
				// 默认值
				coupon.DiscountType = "percentage"
				coupon.DiscountValue = 0
			}
		} else {
			// 默认值
			coupon.DiscountType = "percentage"
			coupon.DiscountValue = 0
		}

		// 设置优惠券URL，优先使用tracking_url，如果没有则使用original_domain
		if trackingUrlInterface, ok := data["tracking_url"]; ok {
			if trackingUrl, ok := trackingUrlInterface.(string); ok && trackingUrl != "" {
				coupon.CouponUrl = trackingUrl
			}
		} else if originalDomainInterface, ok := data["original_domain"]; ok {
			if originalDomain, ok := originalDomainInterface.(string); ok && originalDomain != "" {
				coupon.CouponUrl = originalDomain
			}
		} else {
			// 如果都没有URL，跳过这个优惠券
			s.logger.Warn("优惠券缺少URL信息", zap.String("code", coupon.Code))
			continue
		}

		// 解析日期
		if startedAtInterface, ok := data["started_at"]; ok {
			if startedAtStr, ok := startedAtInterface.(string); ok {
				if startedAtDate, err := time.Parse("2006-01-02", startedAtStr); err == nil {
					coupon.StartDate = uint64(startedAtDate.Unix())
				} else {
					s.logger.Warn("日期解析错误", zap.Error(err), zap.String("started_at", startedAtStr))
					continue
				}
			}
		}

		if endedAtInterface, ok := data["ended_at"]; ok {
			if endedAtStr, ok := endedAtInterface.(string); ok {
				if endedAtDate, err := time.Parse("2006-01-02", endedAtStr); err == nil {
					coupon.EndDate = uint64(endedAtDate.Unix())
				} else {
					s.logger.Warn("日期解析错误", zap.Error(err), zap.String("ended_at", endedAtStr))
					continue
				}
			}
		}

		// 设置默认值
		coupon.Active = true
		coupon.Featured = false
		coupon.Verified = false
		coupon.UsageLimit = 0 // 无限制
		coupon.UsedCount = 0
		coupon.UserUsageLimit = 1

		coupon.CreatedAt = time.Now()
		coupon.UpdatedAt = time.Now()
		couponList = append(couponList, coupon)
	}

	return couponList
}

// parseDiscountString 解析折扣字符串，提取数值和类型
func (s *SyncServiceImpl) parseDiscountString(discountStr string) (float64, string) {
	// 移除空格并转换为小写
	discountStr = strings.TrimSpace(strings.ToLower(discountStr))

	// 如果包含%，则为百分比折扣
	if strings.Contains(discountStr, "%") {
		// 提取数字部分
		numStr := strings.ReplaceAll(discountStr, "%", "")
		numStr = strings.TrimSpace(numStr)

		// 尝试解析数字
		if value, err := parseFloat(numStr); err == nil {
			return value, "percentage"
		}
	}

	// 如果包含$或其他货币符号，则为固定金额折扣
	if strings.Contains(discountStr, "$") || strings.Contains(discountStr, "off") {
		// 移除货币符号和"off"等词汇
		numStr := strings.ReplaceAll(discountStr, "$", "")
		numStr = strings.ReplaceAll(numStr, "off", "")
		numStr = strings.TrimSpace(numStr)

		// 尝试解析数字
		if value, err := parseFloat(numStr); err == nil {
			return value, "fixed"
		}
	}

	// 如果包含"free shipping"等关键词
	if strings.Contains(discountStr, "free") && strings.Contains(discountStr, "ship") {
		return 0, "free_shipping"
	}

	// 默认尝试解析为数字，假设为百分比
	if value, err := parseFloat(discountStr); err == nil {
		return value, "percentage"
	}

	// 无法解析，返回默认值
	return 0, "percentage"
}

// parseFloat 辅助函数，解析字符串中的浮点数
func parseFloat(str string) (float64, error) {
	// 移除所有非数字和小数点的字符
	var numStr strings.Builder
	hasDecimal := false

	for _, char := range str {
		if char >= '0' && char <= '9' {
			numStr.WriteRune(char)
		} else if char == '.' && !hasDecimal {
			numStr.WriteRune(char)
			hasDecimal = true
		}
	}

	if numStr.Len() == 0 {
		return 0, ecode.New(ecode.ErrInternalServer.Code, "无法解析数字")
	}

	// 使用Go标准库解析
	result := 0.0
	numString := numStr.String()

	// 简单的字符串到浮点数转换
	parts := strings.Split(numString, ".")
	if len(parts) > 2 {
		return 0, ecode.New(ecode.ErrInternalServer.Code, "无效的数字格式")
	}

	// 解析整数部分
	if len(parts) >= 1 && parts[0] != "" {
		intPart := 0
		for _, digit := range parts[0] {
			if digit >= '0' && digit <= '9' {
				intPart = intPart*10 + int(digit-'0')
			}
		}
		result = float64(intPart)
	}

	// 解析小数部分
	if len(parts) == 2 && parts[1] != "" {
		fracPart := 0.0
		divisor := 1.0
		for _, digit := range parts[1] {
			if digit >= '0' && digit <= '9' {
				divisor *= 10
				fracPart = fracPart*10 + float64(digit-'0')
			}
		}
		result += fracPart / divisor
	}

	return result, nil
}

// SyncCouponListWithContext 使用标准context.Context的同步方法，适用于定时任务
func (s *SyncServiceImpl) SyncCouponListWithContext(ctx context.Context) error {
	s.logger.Info("开始同步 coupon 信息")

	// 创建一个临时的gin.Context用于调用现有的方法
	// 这是一个适配器模式，将context.Context转换为gin.Context
	ginCtx := &gin.Context{}

	// 调用现有的同步方法
	err := s.SyncCouponList(ginCtx)
	if err != nil {
		// 将ecode.Error转换为标准error
		return err
	}

	return nil
}

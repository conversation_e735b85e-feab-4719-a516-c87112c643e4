package appservice

import (
	"brandreviews/application/coupon/dto"
	"brandreviews/domain/coupon/entity"
	"brandreviews/domain/coupon/service"
	"brandreviews/infra/ecode"

	"github.com/gin-gonic/gin"
)

type CouponAppService interface {
	CreateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error
	GetCouponDetailById(ctx *gin.Context, id uint64) (*dto.CouponDetailResp, *ecode.Error)
	GetCouponDetailBySlug(ctx *gin.Context, slug string) (*dto.CouponDetailResp, *ecode.Error)
	GetCouponDetailByCode(ctx *gin.Context, code string) (*dto.CouponDetailResp, *ecode.Error)
	GetCouponListByCondition(ctx *gin.Context, req *dto.GetCouponListReq) (*dto.CouponListResp, *ecode.Error)
	GetCouponCount(ctx *gin.Context) (int64, *ecode.Error)
}

type CouponAppServiceImpl struct {
	couponService service.CouponService
}

func NewCouponAppService(
	couponService service.CouponService,
) CouponAppService {
	return &CouponAppServiceImpl{
		couponService: couponService,
	}
}

func (app *CouponAppServiceImpl) CreateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error {
	return app.couponService.CreateCoupon(ctx, coupon)
}

func (app *CouponAppServiceImpl) GetCouponCount(ctx *gin.Context) (int64, *ecode.Error) {
	return app.couponService.GetCouponCount(ctx)
}

func (app *CouponAppServiceImpl) GetCouponDetailById(ctx *gin.Context, id uint64) (*dto.CouponDetailResp, *ecode.Error) {
	coupon, err := app.couponService.GetCouponDetailById(ctx, id)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoCouponDetailResp(coupon), nil
}

func (app *CouponAppServiceImpl) GetCouponDetailBySlug(ctx *gin.Context, slug string) (*dto.CouponDetailResp, *ecode.Error) {
	coupon, err := app.couponService.GetCouponDetailBySlug(ctx, slug)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoCouponDetailResp(coupon), nil
}

func (app *CouponAppServiceImpl) GetCouponDetailByCode(ctx *gin.Context, code string) (*dto.CouponDetailResp, *ecode.Error) {
	coupon, err := app.couponService.GetCouponDetailByCode(ctx, code)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoCouponDetailResp(coupon), nil
}

func (app *CouponAppServiceImpl) GetCouponListByCondition(ctx *gin.Context, req *dto.GetCouponListReq) (*dto.CouponListResp, *ecode.Error) {
	condition := req.Dto2ConditionGetCouponList()
	couponList, total, err := app.couponService.GetCouponListByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoCouponListResp(req.PageSize, req.Page, total, couponList), nil
}
